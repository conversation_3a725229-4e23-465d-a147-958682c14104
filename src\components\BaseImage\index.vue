<template>
  <div class="h-full flex-shrink-0 base-image relative">
    <img class="h-full w-full" :src="src || ErrorIcon" :preview="!!src" loading="lazy" />
    <div class="base-image-mask" v-if="!!src" @click="() => setVisible(true)">
      <EyeOutlined class="text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
    </div>
    <a-image
      v-if="!!src"
      :width="200"
      :style="{ display: 'none' }"
      :preview="{
        visible,
        onVisibleChange: setVisible,
      }"
      :src="src"
    />
  </div>
</template>

<script setup lang="ts">
import { EyeOutlined } from '@ant-design/icons-vue'
import ErrorIcon from '@/assets/icons/error-image.svg'

withDefaults(
  defineProps<{
    src: string | null
    height?: number | string
  }>(),
  {
    src: '',
    height: '100%',
  },
)

const visible = ref<boolean>(false)
const setVisible = (value): void => {
  visible.value = value
}
</script>

<style lang="scss" scoped>
.base-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  z-index: 1;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  &:hover {
    opacity: 1;
  }
}
</style>
