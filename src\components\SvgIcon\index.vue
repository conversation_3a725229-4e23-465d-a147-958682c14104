<template>
  <svg :class="svgClass" v-bind="$attrs">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>
<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  // 颜色
  color: {
    type: String,
    default: '',
  },
  // class
  className: {
    type: String,
    default: '',
  },
  // svg名称
  name: {
    type: String,
    required: true,
  },
})

const iconName = computed(() => {
  if (props.name) return `#icon-${props.name}`
  return ''
})
const svgClass = computed(() => {
  if (props.className) return `svg-icon ${props.className}`
  return 'svg-icon'
})
</script>

<style scoped lang="scss">
.svg-icon {
  position: relative;
  width: 1em;
  height: 1em;
  vertical-align: bottom;
  fill: currentcolor;
}
</style>
