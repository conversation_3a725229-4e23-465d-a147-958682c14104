<template>
  <div class="main">
    <div class="mask" v-if="isShowLoadingMask">
      <a-spin />
    </div>
    <div class="main-content">
      <!-- 左侧组织架构面板 -->
      <ArchitectureLeftPanel
        ref="architectureRef"
        :selected-id="selectedDeptId"
        :check-permission="checkArchPermission"
        @select-change="handleArchSelectChange"
        @add-dept="handleAddDept"
        @edit-dept="handleEditDept"
        @view-dept="handleViewDept"
        @dept-operation-success="handleDeptOperationSuccess"
      />

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.USER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" :clearCb="resetFormCb"></SearchForm>

        <BaseTable
          ref="tableRef"
          :page-type="PageType.USER_MANAGE"
          v-model:form="formArr"
          :get-list="GetList"
          :form-format="formFormat"
          :data-format="dataFormat"
          :is-index="true"
          :is-checkbox="true"
          :auto-search="true"
          @checkbox-change="onCheckboxChange"
          @init-finish="handleTableInitFinish"
        >
          <template #left-btn>
            <!-- 内部用户隐藏"新增用户"按钮 -->
            <a-button v-if="!isInternalUser" type="primary" :disabled="!btnPermission[211001]" @click="tapManipulate('add')">新建用户</a-button>
            <a-button @click="tapManipulate('sync')">同步信息</a-button>
          </template>

          <!-- <template #right-btn>
        <a-button
          :disabled="!btnPermission[211004] || selectedItems.length === 0"
          @click="batchToggleStatus('启用')"
          class="btn"
        >
          批量启用
        </a-button>
        <a-button
          :disabled="!btnPermission[211004] || selectedItems.length === 0"
          @click="batchToggleStatus('停用')"
          class="btn"
          danger
        >
          批量停用
        </a-button>
      </template> -->

          <template #operate="{ row }">
            <a-button type="text" :disabled="!btnPermission[211002]" @click="tapManipulate('view', row)" class="btn">查看</a-button>
            <a-button type="text" :disabled="!btnPermission[211003]" @click="tapManipulate('compiler', row)" class="btn">编辑</a-button>
          </template>

          <template #customer_name="{ row }">
            {{ supplierNameMap[row.customer_id] }}
          </template>

          <template #status="{ row }">
            <div v-if="row.status !== null">
              <!-- 内部用户不支持编辑开关 -->
              <a-switch :disabled="!btnPermission[211004] || isInternalUser" class="btn" :checked="row.status === 1 || row.status === '启用'" @click="() => tapSwitch(row)">
                <template #checkedChildren><check-outlined /></template>
                <template #unCheckedChildren><close-outlined /></template>
              </a-switch>
            </div>
            <span v-else>--</span>
          </template>
          <template #role_names="{ row }">
            <a-tag v-for="item2 in row['role_names']" :key="item2">{{ item2 }}</a-tag>
          </template>
          <template #source_type="{ row }">
            <span>{{ formatOptionLabel(row.source_type, sourceOption) }}</span>
          </template>
        </BaseTable>

        <a-modal v-model:open="visibleData.isShow" :title="visibleData.title">
          <div class="modalContent">{{ visibleData.content }}</div>
          <template #footer>
            <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 10px" type="primary" @click="visibleData.okFn">{{ visibleData.confirmBtnText }}</a-button>
            <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
          </template>
        </a-modal>

        <ExtraFormDrawer ref="extraFormDrawerRef" @query="search" />
        <InnerFormDrawer ref="innerFormDrawerRef" @query="search" />
        <UserInfoDrawer ref="userInfoDrawerRef" />
        <SimpleUserInfoDrawer ref="simpleUserInfoDrawerRef" />
      </div>
    </div>

    <!-- 部门管理弹窗 -->
    <DepartmentFormDrawer ref="deptFormDrawerRef" :parent-id="selectedParentDeptId" :company-id="selectedCompanyId" @success="handleDeptFormSuccess" />
    <DepartmentDetailDrawer ref="deptDetailDrawerRef" />

    <!-- 新增用户组件 -->
    <AddMembers ref="addMembersRef" @query="search" />

    <!-- 编辑用户组件 -->
    <EditMembers ref="editMembersRef" @query="search" />
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { sourceOption } from '@/common/options'
import { GetList, GetDepartmentContactTreeList, SyncCompanyInfo, UpdateOuterStatus, Delete, ResetPwd } from '@/servers/UserManager'
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { message } from 'ant-design-vue'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { onMounted, ref, nextTick, watch, computed } from 'vue'
import type { TreeNode } from '@/servers/CompanyArchitecture'
import { formatOptionLabel, checkPagePermission, buttonDebounce } from '@/utils'
import { usePermission } from '@/hook/usePermission'
import SearchForm from '@/components/SearchForm/index.vue'
import ExtraFormDrawer from './components/ExtraFormDrawer.vue'
import InnerFormDrawer from './components/InnerFormDrawer.vue'
import UserInfoDrawer from './components/UserInfoDrawer.vue'
import SimpleUserInfoDrawer from './components/SimpleUserInfoDrawer.vue'
import ArchitectureLeftPanel from './components/ArchitectureLeftPanel.vue'
import DepartmentFormDrawer from './components/DepartmentFormDrawer.vue'
import DepartmentDetailDrawer from './components/DepartmentDetailDrawer.vue'
import AddMembers from './components/AddMembers.vue'
import EditMembers from './components/EditMenbers.vue'

const isShowLoadingMask = ref(false)
const extraFormDrawerRef = ref()
const innerFormDrawerRef = ref()
const userInfoDrawerRef = ref()
const simpleUserInfoDrawerRef = ref()
const architectureRef = ref()
const deptFormDrawerRef = ref()
const addMembersRef = ref()
const editMembersRef = ref()
const deptDetailDrawerRef = ref()

const { btnPermission } = usePermission()

// 检查用户管理权限
const hasUserManagementPermission = ref(false)

type FormItem = {
  label: string
  value: string | null // 允许 null
  type: string
  options?: Array<{ label: string; value: string | number }>
  key: string
  placeholder?: string
  width?: number
  showTooltip?: boolean
  showSearch?: boolean
  onChange?: (value: any) => void
  class?: string
}

// 获取当前用户信息，判断是否为内部用户
const getCurrentUserScope = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      return userData.scope || 2 // 默认为外部用户
    }
    return 2 // 默认为外部用户
  } catch (error) {
    console.error('获取用户scope失败:', error)
    return 2 // 默认为外部用户
  }
}

// 判断是否为内部用户（scope=1为内部用户，scope=2为外部用户）
const isInternalUser = computed(() => getCurrentUserScope() === 1)

// 组织架构相关状态
const selectedDeptId = ref<string>('')
const selectedCompanyId = ref<string>('')
const selectedParentDeptId = ref<string>('')
const currentArchNode = ref<TreeNode | null>(null)

// 视图模式状态
const viewMode = ref('1') // 1: 展示全部成员, 2: 仅展示部门直属成员

const visibleDataStr = () => ({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  content: '',
  okFn: () => {
    visibleData.value.isShow = false
  },
})
const visibleData: any = ref(visibleDataStr())

const tableRef = ref()
const formRef = ref()
const search = () => {
  if (tableRef.value && tableRef.value.search) {
    tableRef.value.search()
  } else {
    console.error('tableRef.value 或 search 方法不存在')
  }
}

// 选中的用户项
const selectedItems = ref<any[]>([])

// 复选框变化处理
const onCheckboxChange = (records: any[]) => {
  selectedItems.value = records
}

const supplierNameMap = ref({} as any)
// const departmentMap = ref<any>({})

// 独立的查询参数，用于架构选择
const queryParams = ref({
  company_id: null as string | null,
  dept_id: null as string | null,
})

// 表单字段映射函数 - 将前端表单字段映射为API期望的字段
const formFormat = (obj) => {
  const formattedObj = { ...obj }

  // 内部用户仅展示启用状态的账号
  if (isInternalUser.value) {
    formattedObj.status = 1 // 强制只显示启用状态的用户
  } else {
    // 外部用户保持原有逻辑，设置默认状态为启用（如果用户没有选择状态）
    if (formattedObj.status === null || formattedObj.status === undefined) {
      // formattedObj.status = 1 // 默认显示启用状态的用户
    }
  }

  // 确保enterprise_code字段存在，如果没有则设置为'1'
  if (!formattedObj.enterprise_code) {
    formattedObj.enterprise_code = '1'
  }

  // 优先使用架构选择的查询参数
  if (queryParams.value.company_id) {
    formattedObj.company_id = queryParams.value.company_id
  } else if (formattedObj.subcompanyid1) {
    // 如果没有架构选择，则使用表单中的公司选择
    formattedObj.company_id = formattedObj.subcompanyid1
  }

  // 添加部门ID参数支持
  if (queryParams.value.dept_id) {
    formattedObj.dept_id = queryParams.value.dept_id
  }

  // 删除原始字段
  delete formattedObj.subcompanyid1

  // 将 departmentid 映射为 department_id
  if (formattedObj.departmentid) {
    formattedObj.department_id = formattedObj.departmentid
    delete formattedObj.departmentid
  }

  // 处理搜索关键字 - 直接使用接口的 keyword 参数
  if (formattedObj.search_keyword) {
    const keyword = formattedObj.search_keyword.trim()
    if (keyword) {
      // 直接使用 keyword 参数，接口会自动在账户编号/工号/用户编号/用户名/手机/邮箱等字段中搜索
      formattedObj.keyword = keyword
    }
    delete formattedObj.search_keyword
  }

  return formattedObj
}

const dataFormat = (data) => {
  const department_id = [...new Set(data.map((v) => v.department_id).filter((v) => v))]
  if (department_id.length) {
    // GetUserDeptNameList({
    //   department_id,
    // }).then((res) => {
    //   res.data.forEach((v) => {
    //     departmentMap.value[v.department_id] = v.department_name
    //   })
    // })
  }
  return data
}

// 动态生成表单配置，根据用户类型调整
const getFormConfig = () => {
  const baseConfig: FormItem[] = [
    {
      label: '展示全部成员',
      value: '1',
      type: 'select',
      options: [
        { label: '展示全部成员', value: '1' },
        { label: '仅展示部门直属成员', value: '2' },
      ],
      key: 'enterprise_code',
      onChange: (value) => {
        viewMode.value = value
        handleViewModeChange()
      },
    },
    {
      label: '搜索姓名',
      value: null,
      type: 'input',
      options: [],
      key: 'real_name',
      placeholder: '请输入姓名',
    },
    {
      label: '搜索账号/工号/手机/邮箱',
      value: null,
      showTooltip: true,
      type: 'input',
      options: [],
      key: 'search_keyword',
      placeholder: '请输入账号/工号/手机/邮箱',
      class: 'w-280',
    },
    {
      label: '角色',
      value: null,
      type: 'select',
      options: [],
      key: 'role_id',
      showSearch: true,
    },
  ]

  // 外部用户才显示状态筛选选项
  if (!isInternalUser.value) {
    baseConfig.push({
      label: '状态',
      value: null,
      type: 'select',
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ] as any,
      key: 'status',
      showSearch: true,
    })
  }

  return baseConfig
}

const formArr: any = ref(getFormConfig())

// 注释掉的原有配置项（保留作为参考）
// {
//   label: '所属公司',
//   value: null,
//   type: 'select_one',
//   selectArr: [],
//   search: true,
//   key: 'subcompanyid1',
//   onChange: () => {
//     formArr.value.find((e) => e.key === 'departmentid').value = null
//     getDepartmentTreeList()
//   },
// },
// {
//   label: '所在部门',
//   value: null,
//   type: 'select_Tree',
//   fieldNames: {
//     children: 'childrenList',
//     label: 'departmentname',
//     value: 'departmentid',
//   },
//   selectArr: [],
//   key: 'departmentid',
//   onClick: () => {
//     if (!formArr.value.find((e) => e.key === 'subcompanyid1').value) {
//       message.info('请先选择所属公司')
//       return false
//     }
//   },
// },
// {
//   label: '创建时间',
//   value: null,
//   type: 'range-picker',
//   key: 'create_at',
//   formKeys: ['create_start_at', 'create_end_at'],
//   placeholder: ['创建开始时间', '创建结束时间'],
// },
// {
//   label: '修改时间',
//   value: null,
//   type: 'range-picker',
//   key: 'update_at',
//   formKeys: ['update_start_at', 'update_end_at'],
//   placeholder: ['修改开始时间', '修改结束时间'],
// },

const role_Arr = ref([])

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.USER_MANAGE) {
    const arr: any[] = []
    obj.USER_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

// 监听权限变化
watch(
  btnPermission,
  (newPermissions) => {
    // 权限更新处理
  },
  { immediate: true, deep: true },
)

// 监听用户类型变化，动态调整表格列显示
watch(
  isInternalUser,
  (newIsInternal) => {
    // 当用户类型变化时，更新表格列的显示状态
    if (tableRef.value && tableRef.value.tableRef) {
      const $table = tableRef.value.tableRef
      if ($table && $table.getTableColumn) {
        $table.getTableColumn().fullColumn.forEach((x) => {
          if (x.field === 'position_name' || x.field === 'job_id') {
            x.visible = newIsInternal
          }
        })
        $table.refreshColumn()
      }
    }
  },
  { immediate: false },
)

onMounted(() => {
  // 检查当前用户是否有权限访问用户管理页面
  hasUserManagementPermission.value = checkPagePermission('/userLists')

  // 根据用户类型重新初始化表单配置
  formArr.value = getFormConfig()

  if (hasUserManagementPermission.value) {
    getRoleList()
    search()
    initScreening()
  } else {
    console.warn('用户无权限访问用户管理页面，跳过接口调用')
  }
})

// 监听BaseTable初始化完成事件，动态调整表格列配置
const handleTableInitFinish = (tableConfig: any[]) => {
  // 根据用户类型动态调整岗位和工号字段的显示
  if (tableConfig && Array.isArray(tableConfig)) {
    tableConfig.forEach((column) => {
      // 只有内部企业才显示岗位和工号字段
      if (column.key === 'position_name' || column.key === 'job_id') {
        column.is_show = isInternalUser.value
      }
    })

    // 需要刷新表格列显示
    nextTick(() => {
      if (tableRef.value && tableRef.value.tableRef) {
        const $table = tableRef.value.tableRef
        if ($table && $table.getTableColumn) {
          $table.getTableColumn().fullColumn.forEach((x) => {
            if (x.field === 'position_name' || x.field === 'job_id') {
              x.visible = isInternalUser.value
            }
          })
          $table.refreshColumn()
        }
      }
    })
  }
}

// 点击批量备货按钮 - 已禁用，状态改为只读显示
// const tapBatch = (status) => {
//   if (tableRef.value.checkItemsArr?.length) {
//     const arr = [] as any[]
//     tableRef.value.checkItemsArr.forEach((item) => {
//       arr.push(item.id)
//     })
//     tapSwitch(status, arr)
//   } else {
//     message.info('请勾选对应项~')
//   }
// }

// 防抖处理的操作函数
const tapManipulate = buttonDebounce(async (type?: string, row: any = '') => {
  visibleData.value = visibleDataStr()
  switch (type) {
    case 'add': {
      const entId = selectedCompanyId.value || ''
      addMembersRef.value?.show(entId)
      break
    }
    case 'view':
      // 使用新的简洁用户详情组件
      simpleUserInfoDrawerRef.value.showDrawer(row)
      break
    case 'compiler':
      // 使用新的 EditMembers 组件
      editMembersRef.value?.showDrawer(row)
      break
    case 'removes':
      visibleData.value.isShow = true
      visibleData.value.title = '删除用户：'
      visibleData.value.content = `
      即将删除该用户的帐号，删除后：

          * 该用户将无法访问系统的任何功能或数据。
          * 用户的操作历史记录和相关数据将保留在系统中。

      请在执行此操作前确认：

          * 该用户的相关工作已妥善移交或完成。

      此操作不可恢复，确定要删除该用户的帐号吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        debouncedDeleteRole(row.id)
      }
      break
    case 'resetPassword':
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
      即将为该用户重置密码，重置后：
        * 用户的密码将被重置为系统的初始默认密码。
        * 用户需使用默认密码重新登录并更新为新的个人密码。
      确定要重置该用户的密码吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        debouncedResetPwd(row.id)
      }
      break
    case 'sync':
      // 同步信息新增弹窗二次确认提醒
      visibleData.value.isShow = true
      visibleData.value.title = '同步信息确认'
      visibleData.value.content = '是否确认同步信息？同步时间视数据量而定，请耐心等候。'
      visibleData.value.confirmBtnText = '确认'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = async () => {
        visibleData.value.isShow = false
        isShowLoadingMask.value = true
        try {
          await SyncCompanyInfo()
          message.success('同步信息成功')
        } catch (error) {
          console.error('同步信息失败:', error)
          message.error('同步信息失败，请重试')
        } finally {
          isShowLoadingMask.value = false
        }
      }
      break
    default:
      break
  }
}, 300)

// 单个用户状态切换
const tapSwitch = (row) => {
  const currentStatus = row.status === 1 || row.status === '启用'
  const statusAction = currentStatus ? '停用' : '启用'

  // 使用防抖处理，避免快速点击
  setTimeout(() => {
    visibleData.value.isShow = true
    visibleData.value.title = `${statusAction}用户`

    if (statusAction === '停用') {
      visibleData.value.content = `即将停用用户 ${row.real_name}，停用后：

• 该用户将无法访问系统的任何功能或数据
• 用户的操作历史记录和相关数据将保留在系统中

确定要停用该用户的账号吗？`
    } else {
      visibleData.value.content = `即将启用用户 ${row.real_name}，启用后：

• 该用户将恢复对系统功能和数据的访问权限
• 用户可以正常登录和使用系统

确定要启用该用户的账号吗？`
    }

    visibleData.value.confirmBtnText = statusAction
    visibleData.value.okType = statusAction === '停用' ? 'danger' : 'primary'
    // 修复：使用正确的字段获取账号ID
    const accountId = row.id || row.umc_account_id || row.account_id
    if (!accountId) {
      message.error('无法获取用户账号信息')
      visibleData.value.isShow = false
      return
    }
    visibleData.value.okFn = () => updateUserStatus([accountId], statusAction)
  }, 300)
}

// 更新用户状态
const updateUserStatus = (accountIds: string[], status: string) => {
  // 修复：确保 company_id 有值
  const companyId = selectedCompanyId.value || queryParams.value.company_id || 0

  // if (!companyId) {
  //   message.error('无法获取公司信息，请重新选择组织架构节点')
  //   visibleData.value.isShow = false
  //   return
  // }

  const requestData = {
    company_id: companyId,
    umc_account_ids: accountIds,
    status: status === '启用' ? '启用' : '停用', // 根据API文档使用字符串状态
  }

  UpdateOuterStatus(requestData)
    .then(() => {
      message.success(`${status}成功`)
      visibleData.value.isShow = false
      selectedItems.value = []
      tableRef.value?.clearCheckbox()
      search()
    })
    .catch((error) => {
      console.error('状态切换失败:', error)
      message.error(`${status}失败`)
      visibleData.value.isShow = false
    })
}
//     tableRef.value.refresh()
//   })
// }

// 删除外部用户
const deleteRole = (id) => {
  Delete({ id }).then(() => {
    visibleData.value.isShow = false
    // message.success('删除成功')
    tableRef.value.refresh()
  })
}

// 防抖版本的删除用户函数
const debouncedDeleteRole = buttonDebounce(deleteRole, 1000)

// 修改密码
// const setUpdatePwd = () => {
//   UpdatePwd({
//     id: formData.value.id,
//     old_password: formData.value.old_password,
//     new_password: formData.value.new_password,
//   }).then(res => {
//     message.success('修改成功');
//     isAddUser.value = false;
//     tapQueryForm();
//   });
// };

// 重置密码
const resetPwd = (id) => {
  ResetPwd({ id }).then((res) => {
    visibleData.value = visibleDataStr()
    setTimeout(() => {
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
    已成功重置该用户的密码，重置后的密码为：

            ${res.data.newPwd}

    请及时将密码发给该用户，并通知其登录后修改密码。`
      visibleData.value.isCancelBtn = false
      visibleData.value.confirmBtnText = '关闭'
    }, 300)
  })
}

// 防抖版本的重置密码函数
const debouncedResetPwd = buttonDebounce(resetPwd, 1000)

// 获取角色列表
const getRoleList = () => {
  GetRoleSelectOption({ scope: 2 }).then((res) => {
    const roleOptions = res.data
      // .filter((e) => e.status == 1)
      .map((e) => ({
        label: e.role_name,
        value: e.role_id,
      }))
    formArr.value.forEach((item) => {
      if (item.key == 'role_id') {
        item.options = roleOptions
      }
    })
    role_Arr.value = res.data // 保持原有的role_Arr变量
  })
}

// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  // 安全地查找 subcompanyid1 字段
  const subcompanyField = formArr.value.find((e) => e.key === 'subcompanyid1')
  const value = subcompanyField?.value

  if (!value) {
    formArr.value.forEach((item) => {
      if (item.key == 'departmentid') {
        item.options = []
      }
    })
    return
  }

  GetDepartmentContactTreeList({ id: value, key: 'department' })
    .then((res) => {
      formArr.value.forEach((item) => {
        if (item.key == 'departmentid') {
          item.options = res.data
        }
      })
    })
    .catch((error) => {
      console.error('GetDepartmentContactTreeList 调用失败:', error)
    })
}
const resetFormCb = () => {
  formArr.value.forEach((e) => {
    if (e.key === 'departmentid') {
      e.options = []
    }
    if (e.key !== 'enterprise_code') {
      e.value = null
    } else {
      e.value = '1'
    }
  })
}

// 视图模式变化处理
const handleViewModeChange = () => {
  // 重置查询参数
  resetQuery()
  // 触发搜索
  search()
}

// 重置查询
const resetQuery = () => {
  formArr.value.forEach((item: any, index: number) => {
    if (index === 0) {
      // 第一个字段（enterprise_code）重置为'1'
      // item.value = '1'
      viewMode.value = '1'
    } else {
      item.value = Array.isArray(item.value) ? [] : null
    }
  })
}

// ==================== 组织架构相关方法 ====================

// 权限检查函数
const checkArchPermission = (permission: string) => {
  const permissionMap = {
    arch_add: btnPermission.value[211004], // 新建部门权限
    arch_edit: btnPermission.value[211005], // 编辑部门权限
    arch_delete: btnPermission.value[211005], // 编辑部门权限作为删除权限
    arch_view: btnPermission.value[211006], // 查看部门权限
    arch_updown: btnPermission.value[211005], // 编辑部门权限作为上移下移权限
  }

  let result = permissionMap[permission] || false

  // 内部用户不支持创建部门，仅支持查看部门详情操作
  if (isInternalUser.value && permission === 'arch_add') {
    result = false
  }

  // console.log(`权限 ${permission} 检查结果:`, result)
  return result
}

// 组织架构选择变化处理
const handleArchSelectChange = (id: string, data: TreeNode | null) => {
  selectedDeptId.value = id
  currentArchNode.value = data

  if (data) {
    // 根据节点类型设置查询参数
    const originalType = (data as any).originalType || data.type

    if (originalType === 1 || data.type === '企业=1') {
      // 企业节点 (type=1) - 只传 company_id，dept_id 设为 null
      selectedCompanyId.value = id
      queryParams.value.company_id = id
      queryParams.value.dept_id = null
      getDepartmentTreeList()
    } else if (originalType === 3 || data.type === '部门=3') {
      // 部门节点 (type=3) - 同时传 company_id 和 dept_id
      // 需要找到该部门所属的企业ID作为 company_id
      const companyId = findParentCompanyId(data)
      selectedCompanyId.value = companyId || id
      queryParams.value.company_id = companyId || id
      queryParams.value.dept_id = id
    } else {
      // 其他类型节点（如单位等）- 按部门处理
      const companyId = findParentCompanyId(data)
      selectedCompanyId.value = companyId || id
      queryParams.value.company_id = companyId || id
      queryParams.value.dept_id = id
    }

    // 自动触发搜索
    search()
  }
}

// 查找节点所属的企业ID
const findParentCompanyId = (node: TreeNode | null): string | null => {
  if (!node) return null

  // 如果当前节点就是企业节点，返回其ID
  const originalType = (node as any).originalType || node.type
  if (originalType === 1 || node.type === '企业=1') {
    return String(node.id)
  }

  // 简化逻辑：对于部门节点，使用当前选中的企业ID
  // 因为架构树是按企业展开的，所以部门一定属于当前选中的企业
  if (selectedCompanyId.value) {
    return selectedCompanyId.value
  }

  // 如果没有选中的企业ID，尝试从架构面板获取
  const archRef = architectureRef?.value
  if (archRef && archRef.getSelectedCompany) {
    const companyId = archRef.getSelectedCompany()
    return companyId
  }

  return String(node.id)
}

// 添加部门处理
const handleAddDept = (parentId?: string) => {
  selectedParentDeptId.value = parentId || ''
  deptFormDrawerRef.value?.open()
}

// 编辑部门处理
const handleEditDept = (id: string) => {
  // 需要先获取部门详情数据
  const editData = {
    id,
    department_name: currentArchNode.value?.department_name || '',
    p_id: currentArchNode.value?.p_id || '',
    company_id: selectedCompanyId.value,
    header_ids: [],
    oa_id: currentArchNode.value?.oa_id || '',
  }
  deptFormDrawerRef.value?.open(editData)
}

// 查看部门详情处理
const handleViewDept = (id: string, type: number) => {
  // 直接使用子组件传递的准确 type 参数，不再重新计算
  deptDetailDrawerRef.value?.open(id, type)
}

// 部门表单提交成功处理
const handleDeptFormSuccess = async () => {
  // console.log('🎉 部门表单提交成功，开始刷新数据...')

  // 添加短暂延迟，确保后端数据已更新
  await new Promise((resolve) => {
    setTimeout(resolve, 300)
  })

  // 刷新组织架构树
  if (architectureRef.value?.refresh) {
    // console.log('📋 刷新组织架构树...')
    await architectureRef.value.refresh()
  } else {
    // console.error('❌ architectureRef.value?.refresh 方法不存在')
  }

  // 刷新用户列表
  // console.log('👥 刷新用户列表...')
  search()
  message.success('操作成功')
}

// 部门操作成功处理（删除、移动等）
const handleDeptOperationSuccess = () => {
  // console.log('部门操作成功，刷新数据...')
  // 刷新组织架构树
  if (architectureRef.value?.refresh) {
    // console.log('📋 刷新组织架构树...')
    architectureRef.value.refresh()
  }
  // 刷新用户列表
  search()
}
</script>
<style lang="scss" scoped>
.userRoleBox {
  padding-right: 40px;

  .li {
    display: flex;
    align-items: center;
    margin-top: 24px;
    font-size: 16px;
    color: #000;

    .label {
      width: 120px;
      margin-right: 30px;
      text-align: right;

      .text {
        position: relative;

        .icon_i {
          position: absolute;
          top: 50%;
          left: -15px;
          padding-top: 7px;
          font-size: 12px;
          color: red;
          transform: translateY(-50%);
        }
      }
    }

    .input {
      flex: 1;
    }

    .checkedBox {
      flex: 1;
    }

    .select {
      flex: 1;
    }

    .treeSelect {
      flex: 1;
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

.status-enabled {
  font-weight: 500;
  color: #52c41a;
}

.status-disabled {
  font-weight: 500;
  color: #ff4d4f;
}

.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .main-content {
    display: flex !important;
    flex: 1; /* 让内容区域占满剩余空间 */
    flex-direction: row !important; /* 确保是水平布局 */
    gap: 0; /* 移除默认间距 */
    align-items: stretch !important;
    height: calc(100vh - 140px) !important; /* 优化高度计算，减少空白 */
  }

  .right-content {
    display: flex !important;
    flex: 1 !important;
    flex-direction: column !important;
    width: calc(100% - 246px) !important; /* 总宽度减去左侧面板宽度和间距 */
    min-width: 0 !important; /* 防止flex子项溢出 */

    // 重置Form组件的flex布局
    :deep(.ant-form) {
      .flex {
        display: block !important;
      }

      margin-bottom: 16px; /* 减少表单底部间距 */
    }

    // 确保BaseTable正确显示
    :deep(.tableBox) {
      display: flex !important;
      flex: 1 !important;
      flex-direction: column !important;
      min-height: 0; /* 防止表格溢出 */

      .box {
        flex: 1;
        min-height: 0;
      }
    }
  }
}

.mask {
  position: absolute;
  inset: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(185 185 185 / 46%);
}
</style>
