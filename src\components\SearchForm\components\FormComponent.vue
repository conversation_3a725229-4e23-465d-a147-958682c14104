<template>
  <template v-for="item in filterFormArr" :key="item.key">
    <a-tooltip v-if="item.showTooltip">
      <template #title>{{ item.placeholder }}</template>
      <component :is="getFormItemCom(item.type)" :item="item" />
    </a-tooltip>
    <component v-else :is="getFormItemCom(item.type)" :item="item" />
  </template>
</template>

<script setup lang="ts">
import { FormItem } from '../type'
import FormInput from './FormInput.vue'
import FormBatchInput from './FormBatchInput.vue'
import FormSelect from './FormSelect.vue'
import FormRangePicker from './FormRangePicker.vue'
import FormCascader from './FormCascader.vue'
import FormTreeSelect from './FormTreeSelect.vue'
import FormRangeInput from './FormRangeInput.vue'

const formArr = defineModel<FormItem[]>('formArr', { required: true })

const filterFormArr = computed(() => {
  return formArr.value.filter((item) => item.isShow)
})

// 组件映射表
const componentMap = {
  input: FormInput,
  select: FormSelect,
  cascader: FormCascader,
  date: FormRangePicker,
  inputDlg: FormBatchInput,
  'range-picker': FormRangePicker,
  'select-one': FormSelect,
  'select-tree': FormTreeSelect,
  'batch-input': FormBatchInput,
  'range-input': FormRangeInput,
} as const

const getFormItemCom = (type: string) => {
  const component = componentMap[type as keyof typeof componentMap]
  return component
}
</script>

<style scoped></style>
