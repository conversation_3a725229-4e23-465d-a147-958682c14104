<template>
  <!-- 权限检查完成前显示加载状态，防止闪烁 -->
  <div v-if="!permissionCheckCompleted" class="permission-loading">
    <div class="loading-content">
      <a-spin size="large" />
      <div class="loading-text">正在验证权限...</div>
    </div>
  </div>

  <!-- 权限检查完成后显示主界面 -->
  <div v-else class="page">
    <div class="memuBox" :class="collapsed ? 'memuActive' : ''">
      <div class="titleBox">
        <div class="title">
          <img class="logo" src="../assets/image/logo-o.png" alt="" />
          <div class="text" v-show="!collapsed">SRS供应商门户</div>
        </div>

        <a-button class="toggleBtn" :class="collapsed ? 'active' : ''" type="primary" @click="toggleCollapsed">
          <MenuUnfoldOutlined v-if="collapsed" />
          <MenuFoldOutlined v-else />
        </a-button>
      </div>
      <div class="box">
        <a-menu mode="inline" theme="dark" :inline-collapsed="collapsed" @click="tapMenu" v-model:selectedKeys="selectedMenuKeys" v-model:openKeys="openKeys">
          <template v-for="item in menuList" :key="item.path">
            <template v-if="!item.children">
              <a-menu-item :key="item.path">
                <template #icon>
                  <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                </template>
                {{ item.name }}
              </a-menu-item>
            </template>
            <template v-else>
              <a-sub-menu :key="item.path" :class="item.path == '/productManagement' ? 'product-management-menu' : ''">
                <template #icon>
                  <div class="menu-icon-wrapper">
                    <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                    <span v-if="item.path == '/productManagement' && productLibraryCount > 0 && allPermission[823000]" class="menu-red-dot"></span>
                  </div>
                </template>
                <template #title>
                  <span class="pl-3">{{ item.name }}</span>
                </template>
                <a-menu-item v-for="item2 in item.children" :key="item2.path">
                  <span class="pl-5">{{ item2.name }}</span>
                  <!-- <a-badge v-if="item2.path == '/changeOwnerFlowList'" :count="approvalNum.change_owner_flow_num" :offset="[15, -5]" />
                  <a-badge v-if="item2.path == '/shopRegistrationFlowList'" :count="approvalNum.shop_registration_flow_num" :offset="[15, -5]" /> -->
                  <a-badge v-if="item2.path == '/ProductInfo' && allPermission[823000]" :count="productLibraryCount" :offset="[3, -19]" />
                </a-menu-item>
              </a-sub-menu>
            </template>
          </template>
        </a-menu>
      </div>
      <div class="version">
        <InfoCircleOutlined class="icon" />
        版本号：{{ version ? version : '--' }}
      </div>
    </div>
    <div class="mainBox" :class="collapsed ? 'active' : ''">
      <div class="topNav">
        <div class="navBox">
          <a-tabs :tabBarGutter="0" v-model:activeKey="navPagPath" tab-position="top" type="editable-card" size="small" hideAdd @edit="handleClose" @tabClick="tapNavTabItem">
            <a-tab-pane v-for="item in navPageArr" :key="item.path" :tab="item.name" :closable="navPageArr.length !== 1"></a-tab-pane>
          </a-tabs>
          <!-- <div class="item" :class="navPagPath == item.path ? 'active' : ''" v-for="(item, i) in navPageArr" :key="item.path" @click="tapNavTabItem(item)">
            <div class="text">{{ item.name }}</div>
            <div class="icon" @click.stop="handleClose(i)" v-if="navPageArr.length > 1">
              <CloseOutlined />
            </div>
          </div> -->
        </div>
        <div class="btnBox">
          <DownloadCenter v-if="!userData.role_ids.includes(-1)" />
          <a-button @click="handleNavigateHelp" :icon="h(QuestionCircleOutlined)" type="text" class="mr-1px"></a-button>
          <a-dropdown @openChange="(val) => (dropDownVisible = val)">
            <template #overlay>
              <div class="conternBox">
                <div class="nameBox">
                  <div class="nameImg"><img src="@/assets/image/name.png" /></div>
                  <div class="nameText">
                    <div class="textTop">{{ account?.real_name || account?.user_nick_name || '' }}</div>
                    <div class="textBot" v-show="account?.jobtitlename">
                      <span class="label">岗位：</span>
                      <span>{{ account?.jobtitlename }}</span>
                    </div>
                    <div class="textBot" v-show="account?.department">
                      <span class="label">所在部门：</span>
                      <!-- <span>{{ account?.department }}</span> -->
                      <span>{{ formattedDepartment }}</span>
                    </div>
                    <div class="textBot" v-show="getDisplayCompany()">
                      <span class="label">{{ getCompanyDisplayLabel() }}：</span>
                      <span>{{ getDisplayCompany() }}</span>
                    </div>
                  </div>
                </div>
                <!-- <div class="editInfoBtn" @click="handleUserDetailsClick">
                  <i class="iconfont icon-bianji"></i>
                  查看详细
                </div> -->
                <!-- 嵌入切换账号组件 -->
                <SwitchAccount v-if="false" ref="SwitchAccountRef" />
                <div class="tuiChu" @click="handlePersonalCenter">
                  <i class="iconfont icon-yonghumenhu"></i>
                  个人中心
                </div>
                <div class="tuiChu" @click="handleLogoutClick">
                  <i class="iconfont icon-tuichu"></i>
                  退出登录
                </div>
              </div>
            </template>

            <a-badge :dot="true" :offset="[-100, -8]">
              <div :style="dropDownVisible ? 'color: #409EFF;' : ''" class="btnBoxMainText">
                {{ account?.real_name || account?.user_nick_name || '' }}
                <span class="btnBoxsubText" v-if="account?.jobtitlename || account?.department_short">
                  (
                  <!-- 内部 -->
                  <span>
                    <span v-if="account?.jobtitlename">{{ account?.jobtitlename }}</span>
                    <span v-if="account?.jobtitlename && account?.department_short">|</span>
                    <span v-if="account?.department_short">{{ account?.department_short }}</span>
                  </span>
                  )
                </span>
              </div>
            </a-badge>
          </a-dropdown>
        </div>
      </div>
      <!-- <router-view></router-view> -->

      <router-view v-slot="{ Component }">
        <a-alert
          @close="isCloseNoticeNone"
          v-if="noticeContent && !isCloseNotice"
          class="msgAlert"
          :message="noticeContent"
          type="warning"
          closable
          banner
          :style="{ '--duration': `${20 + noticeContent.length * 0.1}s` }"
        />
        <keep-alive class="boxStr">
          <component :is="Component" v-if="route.meta.KeepAlive" :key="route.path" />
        </keep-alive>
        <component v-if="!route.meta.KeepAlive" :is="Component" :key="route.path"></component>
      </router-view>
    </div>
    <a-modal v-model:open="isKeyDown" width="600px" title="批量输入">
      <a-textarea v-model:value="commodityCode" placeholder="多个商品编码，以逗号分隔或每行一个商品编码" :rows="4" />
      <template #footer>
        <a-button key="back" style="float: left">清空</a-button>
        <a-button key="back" @click="isKeyDown = false">取消</a-button>
        <a-button key="submit" type="primary" @click="isKeyDown = false">确认</a-button>
      </template>
    </a-modal>
  </div>
  <UserDetails ref="UserDetailsRef" />
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { MenuFoldOutlined, MenuUnfoldOutlined, InfoCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import { GetNewNotify } from '@/servers/Notice'
import { UserInfo } from '@/servers/User'
import UserDetails from '@/components/UserDetails.vue'
import SwitchAccount from '@/components/SwitchAccount.vue'
import { Info } from '@/servers/Common'
// import { GetLabelStatusCount } from '@/servers/ProductLibrary'
import eventBus from '@/utils/eventBus'
import useAppStore from '@/store/modules/app'
import { LogoutV4, LoginRedirect } from '@/servers/UmcAuth'
import { useSupplierSettleFormStore } from '@/store/modules/supplierSettlementForm'
import { initSignalRConnection } from '@/utils/initSignalRConnection'
import { message } from 'ant-design-vue/es'
import useBadgeStore from '@/store/modules/badgeStore'
import { checkedBtnPermission, beforLogout } from '@/utils/index'

const allPermission = ref({})

const getAllPermission = async () => {
  const productInfoPermission: any = await checkedBtnPermission('/ProductInfo')
  return {
    ...productInfoPermission,
  }
}

const badgeStore = useBadgeStore()
const formStore = useSupplierSettleFormStore()
const appStore = useAppStore()
const UserDetailsRef = ref()
const SwitchAccountRef = ref()
const account = ref()
const router = useRouter()
const route = useRoute()
const menuList: any = ref([])
const userData: any = ref({})
const commodityCode = ref(null)
const isKeyDown = ref(false)
const navPagPath = ref('/')
const navPageArr: any = ref([])
const collapsed = ref(false)
const selectedMenuKeys = ref(['/'])
const openKeys: any = ref()
const version = ref('')
const isCloseNotice = ref(false)
const noticeContent = ref<any>(null)
const noticeContentId = ref()

// 动态获取当前页面的IP和端口
const getCurrentHostInfo = () => {
  const protocol = window.location.protocol // http: 或 https:
  const hostname = window.location.hostname // IP地址或域名
  const port = window.location.port // 端口号

  // 构建完整的主机信息
  let hostInfo = `${protocol}//${hostname}`
  if (port) {
    hostInfo += `:${port}`
  }

  return hostInfo
}
// 权限检查完成状态，防止闪烁
const permissionCheckCompleted = ref(false)
const iconfontArr = {
  // 新图标
  '/productManagement': 'shangpinkucunguanli',
  '/notifyManagement': 'tongzhi',
  '/clientModule': 'yonghuguanli',
  '/basicInfoManagement': 'caigoushengenjin',
  '/supplierAdminManagement': 'shanghuguanli',
  '/systemManagement': 'xitongshezhi',
  '/supplierManagement': 'shanghuguanli',
  '/orderCenter': 'jihuashenpi-copy',
}
// const approvalNum = ref({
//   change_owner_flow_num: 0, // 变更负责人待审批数量
//   shop_registration_flow_num: 0, // 店铺注册待审批数量
// })

// 🔴 [DEBUG] 商品库红点数量
const productLibraryCount = computed(() => {
  return badgeStore.badgeCounts?.productLabelStatusCount?.menu_count
})

const dropDownVisible = ref(false)

const checkRouter = (path: string) => {
  // 防止menuList为空时报错
  if (!menuList.value || menuList.value.length === 0) {
    console.warn('menuList为空，无法设置路由状态')
    return
  }

  let isSubMenuItem = false
  let parentMenuPath = ''

  // 首先检查当前路径是否是子菜单项
  menuList.value.forEach((item) => {
    if (item?.children?.length) {
      item.children.forEach((childItem) => {
        if (childItem.path === path) {
          isSubMenuItem = true
          parentMenuPath = item.path
        }
      })
    }
  })

  // 设置菜单状态
  menuList.value.forEach((item) => {
    if (item.path === path) {
      // 当前路径是一级菜单
      navPagPath.value = path
      selectedMenuKeys.value = [path]
      // 如果是一级菜单且不是子菜单的父级，清空openKeys
      openKeys.value = []
    } else if (item?.children?.length) {
      item.children.forEach((childItem) => {
        if (childItem.path === path) {
          // 当前路径是子菜单项
          navPagPath.value = path
          selectedMenuKeys.value = [path]
          // 保持父菜单展开
          openKeys.value = [item.path]
        }
      })
    }
  })

  // 特殊处理：如果当前路径是子菜单项，确保父菜单保持展开
  if (isSubMenuItem && parentMenuPath) {
    if (!openKeys.value.includes(parentMenuPath)) {
      openKeys.value = [parentMenuPath]
    }
  }
  // 当前页面是二级页面
  if (router.currentRoute.value.meta.isChildPage == true) {
    navPagPath.value = path
    selectedMenuKeys.value = [path]
    openKeys.value = []
    let isPresence = true
    const navPageArrStr = localStorage.getItem('navPageArr') || ''

    if (navPageArrStr) navPageArr.value = JSON.parse(navPageArrStr)
    console.log('navPageArr.value', navPageArr.value)
    navPageArr.value?.forEach((item2) => {
      if (item2.path == path) {
        isPresence = false
      }
    })
    if (isPresence) {
      const item = {
        children: null,
        id: null,
        code: router.currentRoute.value.meta.code,
        name: router.currentRoute.value.name,
        path,
        title: router.currentRoute.value.name,
      }
      navPageArr.value.push(item)
      localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
    }
  } else {
    const navPageArrStr = localStorage.getItem('navPageArr') || ''
    if (navPageArrStr) navPageArr.value = JSON.parse(navPageArrStr)
  }
}

// 路由和测导航栏
const initialization = () => {
  const path = router.currentRoute.value.path
  const userDataStr = localStorage.getItem('userData') || ''
  const navPageArrStr = localStorage.getItem('navPageArr') || ''

  if (userDataStr) {
    userData.value = JSON.parse(userDataStr)
    menuList.value = userData.value.permissions_infos

    // 检查用户是否有任何权限列表，如果没有则跳转到ErrorPage
    if (!menuList.value || menuList.value.length === 0) {
      console.warn('用户没有任何权限列表，跳转到404页面')
      // 设置权限检查完成状态，然后跳转
      permissionCheckCompleted.value = true
      nextTick(() => {
        router.push('/404')
      })
      return
    }

    // 同时设置account数据用于显示用户信息
    account.value = {
      real_name: userData.value.real_name || userData.value.user_nick_name,
      user_nick_name: userData.value.user_nick_name,
      jobtitlename: userData.value.position_name,
      company: userData.value.company,
      unit_name: userData.value.unit_name,
      // 下拉菜单中显示完整部门路径
      department: userData.value.department?.[0]?.full_name || userData.value.department_name,
      // 右上角按钮显示简短部门名称
      department_short: userData.value.department?.[0]?.name || userData.value.department_name,
      department_name: userData.value.department_name,
      user_name: userData.value.real_name || userData.value.user_nick_name,
      job_id: userData.value.job_id,
    }

    // 安全获取第一个菜单项，防止没有菜单权限时报错
    const first = (() => {
      if (!menuList.value || menuList.value.length === 0) {
        console.warn('用户没有菜单权限')
        return null
      }
      const firstMenu = menuList.value[0]
      return firstMenu?.children?.length > 0 ? firstMenu.children[0] : firstMenu
    })()

    if (navPageArrStr) {
      navPageArr.value = JSON.parse(navPageArrStr)
    } else {
      if (first) {
        navPageArr.value.push(path == '/' ? first : findMenuList(path) || first)
      }
    }

    // 确保router已经准备好并且路径有效
    if (!router || !first?.path) {
      console.warn('Router not ready or invalid first menu path:', { router: !!router, firstPath: first?.path })
      // 如果没有菜单权限，显示无权限页面或跳转到登录页
      if (!first) {
        console.error('用户没有任何菜单权限，跳转到登录页')
        router.push('/login').catch((err) => console.error('跳转登录页失败:', err))
      }
      return
    }

    // 如果当前路径是根路径，跳转到第一个菜单项
    if (path == '/') {
      checkRouter(first.path)
      nextTick(() => {
        router.push(first.path).catch((err) => {
          console.error('Router push error:', err)
        })
      })
    } else {
      // 检查当前路径是否有效
      const currentMenu = findMenuList(path)
      if (currentMenu) {
        checkRouter(path)
      } else {
        // 如果当前路径无效，跳转到第一个菜单项
        checkRouter(first.path)
        nextTick(() => {
          router.push(first.path).catch((err) => {
            console.error('Router push error:', err)
          })
        })
      }
    }

    localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))

    // 权限检查完成，允许显示页面
    permissionCheckCompleted.value = true
  } else {
    localStorage.clear()
    // 没有用户数据，权限检查完成，允许跳转到登录页
    permissionCheckCompleted.value = true
    nextTick(() => {
      router.push('/login').catch((err) => {
        console.error('Router push error:', err)
      })
    })
  }
}
// 获取用户信息
const getUserData = () => {
  // 优先使用localStorage中的用户数据（UMC登录后的数据）
  const userDataStr = localStorage.getItem('userData')
  if (userDataStr) {
    const userDataObj = JSON.parse(userDataStr)
    // 将UMC用户数据映射到account格式
    account.value = {
      real_name: userDataObj.real_name || userDataObj.user_nick_name,
      user_nick_name: userDataObj.user_nick_name,
      jobtitlename: userDataObj.position_name,
      company: userDataObj.company,
      unit_name: userDataObj.unit_name,
      // 下拉菜单中显示完整部门路径
      department: userDataObj.department?.[0]?.full_name || userDataObj.department_name,
      // 右上角按钮显示简短部门名称
      department_short: userDataObj.department?.[0]?.name || userDataObj.department_name,
      department_name: userDataObj.department_name,
      user_name: userDataObj.real_name || userDataObj.user_nick_name,
      job_id: userDataObj.job_id,
    }
    console.log('使用UMC用户数据:', account.value)
  } else {
    // 如果没有localStorage数据，则调用老接口（兼容性处理）
    UserInfo().then((res) => {
      account.value = res.data
      console.log(res, '==================user=====res=======')
    })
  }
}

/* const UserDetailsBtn = () => {
  UserDetailsRef.value.open()
}
  
const handleUserDetailsClick = () => {
  UserDetailsBtn()
  dropDownVisible.value = false
} */

const handleLogoutClick = async () => {
  await formStore.checkFormChangedAndConfirm()

  tuichu()
  dropDownVisible.value = false
}

// 获取要显示的公司/单位信息（优先显示单位）
const getDisplayCompany = () => {
  if (!account.value) return ''
  // 优先显示单位信息
  if (account.value.unit_name) {
    return account.value.unit_name
  }
  // 没有单位信息时显示公司信息
  return account.value.company || ''
}

// 获取公司/单位显示标签
const getCompanyDisplayLabel = () => {
  if (!account.value) return '所属公司'
  // 如果有单位信息，显示"所属单位"
  if (account.value.unit_name) {
    return '所属单位'
  }
  // 否则显示"所属公司"
  return '所属公司'
}

// 根据 path查找menuList
const findMenuList = (path: string) => {
  let val = null
  menuList.value.forEach((item) => {
    if (item.path == path) {
      val = item
      return
    }
    if (item.children)
      item.children.forEach((c) => {
        if (c.path === path) {
          val = c
        }
      })
  })

  return val
}

const getRedPoint = async () => {
  badgeStore.fetchBadgeCounts()
}

watch(
  () => router.currentRoute.value.path,
  (newValue) => {
    checkRouter(newValue)
  },
  { immediate: true },
)

// 在组件挂载后初始化
onMounted(() => {
  initialization()

  // 初始化 SignalR 连接
  try {
    initSignalRConnection()
  } catch (error) {
    console.warn('SignalR 初始化失败:', error)
  }
  getAllPermission().then((res) => {
    allPermission.value = res
  })
})

// 退出登录
const tuichu = async () => {
  try {
    console.log('=== 开始退出登录流程 ===')

    // 获取LoginToken
    const userData = localStorage.getItem('userData')
    let LoginToken = ''
    if (userData) {
      const userDataObj = JSON.parse(userData)
      LoginToken = userDataObj.login_token || ''
    }

    console.log('=== 调用LogoutV4接口 ===')
    console.log('LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')

    // 直接调用LogoutV4接口退出登录
    const logoutResult = await LogoutV4(LoginToken)

    console.log('=== LogoutV4接口响应 ===')
    console.log('完整响应对象:', logoutResult)
    console.log('响应成功状态:', logoutResult?.success)
    console.log('响应消息:', logoutResult?.message)

    // 无论接口调用成功与否，都清除本地数据
    beforLogout()

    if (logoutResult && logoutResult.success) {
      console.log('=== 退出登录成功 ===')
      message.success('退出登录成功')
    } else {
      console.log('=== 退出登录接口失败，但仍清除本地数据 ===')
      message.success('退出登录完成')
    }

    // 获取登录URL并跳转
    console.log('=== 获取登录URL ===')
    let ipParam: string | undefined
    if (import.meta.env.VITE_APP_ENV === 'development') {
      // 开发环境：传递当前页面的IP和端口
      ipParam = getCurrentHostInfo()
      console.log('开发环境，传递IP参数:', ipParam)
    }

    const loginResponse = await LoginRedirect(ipParam)
    if (loginResponse.success && loginResponse.data) {
      console.log('=== 获取到登录URL，直接跳转 ===')
      console.log('登录URL:', loginResponse.data)
      window.location.href = loginResponse.data
    } else {
      console.log('=== 获取登录URL失败，跳转到本地登录页 ===')
      const errorMessage = loginResponse.message || '获取登录地址失败'
      message.error(errorMessage)
      router.push('/login')
    }
  } catch (error) {
    console.error('=== 退出登录发生异常 ===')
    console.error('异常信息:', error)

    // 即使出错也要清除本地数据
    beforLogout()
    message.success('退出登录完成')
    router.push('/login')
  }
}
// 点击菜单
const tapMenu = (e: any) => {
  // 防止menuList为空时报错
  if (!menuList.value || menuList.value.length === 0) {
    console.warn('menuList为空，无法处理菜单点击')
    return
  }

  const fn = (item, path) => {
    if (item.path == path) {
      let isPresence = true
      navPageArr.value.forEach((item2) => {
        if (item2.path == path) {
          isPresence = false
        }
      })
      if (isPresence) {
        navPageArr.value.push(item)
      }
    }
  }

  menuList.value.forEach((item) => {
    if (e.keyPath.length > 1) {
      // 点击的是子菜单项
      if (e.keyPath[1] == item.path && item.children) {
        item.children.forEach((item2) => {
          fn(item2, e.key)
        })
      }
    } else {
      // 点击的是一级菜单项
      fn(item, e.key)
    }
  })

  console.log('navPageArr', navPageArr.value)
  localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
  router.push(e.key).catch((err) => {
    console.error('Router push error in tapMenu:', err)
  })
}

// 点击标签跳转子路由
const tapNavTabItem = (path) => {
  router.push(path).catch((err) => {
    console.error('Router push error in tapNavTabItem:', err)
  })
  setTimeout(() => {
    navPagPath.value = path
  }, 0)
}
// 关闭标签子路由
const handleClose = (path) => {
  const prePath = navPageArr.value[navPageArr.value.findIndex((e) => e.path === path) - 1].path
  navPageArr.value = navPageArr.value.filter((item) => item.path !== path)
  console.log('navPageArr.value', navPageArr.value)
  localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
  checkRouter(prePath)
  router.push(prePath).catch((err) => {
    console.error('Router push error in handleClose:', err)
  })
  console.log('navPageArr.value', navPageArr.value)
}
// 菜单导航栏切换大小
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}
// 获取版本号
const checkEdition = () => {
  Info()
    .then((res) => {
      if (res && res.data && res.data.version) {
        version.value = res.data.version
        const localVersion = localStorage.getItem('version')
        if (!localVersion || localVersion === '') {
          localStorage.setItem('version', res.data.version)
        } else {
          if (res.data.version != localVersion) {
            localStorage.setItem('version', res.data.version)
            setTimeout(() => {
              window.location.reload()
            }, 100)
          }
        }
      }
    })
    .catch((error) => {
      console.warn('检查版本失败，可能是权限问题:', error)
    })
}

let timer

const getNotice = () => {
  GetNewNotify()
    .then((res) => {
      if (res && res.data && res.data !== noticeContent.value && isCloseNotice.value) {
        isCloseNotice.value = false
      }
      noticeContent.value = res?.data?.content || null
      noticeContentId.value = res?.data?.id
      timer = setTimeout(() => {
        getNotice()
      }, 60 * 1000)
    })
    .catch((error) => {
      console.warn('获取通知失败，可能是权限问题:', error)
      // 继续定时获取
      timer = setTimeout(() => {
        getNotice()
      }, 60 * 1000)
    })
}
// 关闭
const isCloseNoticeNone = () => {
  // ReadNotice({ id: noticeContentId.value })
  //   .then((res) => {
  //     if (res && res.success) {
  //       isCloseNotice.value = true
  //     }
  //   })
  //   .catch((error) => {
  //     console.warn('关闭通知失败，可能是权限问题:', error)
  //     // 即使失败也关闭通知显示
  //     isCloseNotice.value = true
  //   })
  isCloseNotice.value = true
}

const getPendinApprovalNum = async () => {
  try {
    // TODO: 待处理审批数量接口已废弃，需要找到新的替代接口
    // const res = await GetPendingApprovalCount()
    // if (res && res.data) {
    //   approvalNum.value = res.data
    // }
    // console.warn('待处理审批数量接口已废弃，暂时禁用此功能')
  } catch (error) {
    console.warn('获取待审批数量失败，可能是权限问题:', error)
    // 静默处理，不影响其他功能
  }
}

// 🔴 [DEBUG] 获取商品库红点数据
// const getProductLibraryRedPoint = async () => {
//   try {
//     const countRes = await GetLabelStatusCount({})
//     if (countRes && countRes.data) {
//       // 使用 waiting_count，与待选商品标签页保持一致
//       productLibraryCount.value = countRes.data?.waiting_count || 0
//     } else {
//       productLibraryCount.value = 0
//     }
//   } catch (error) {
//     productLibraryCount.value = 0
//   }
// }

eventBus.on('getNotice', () => {
  if (timer) clearTimeout(timer)
  getNotice()
})
eventBus.on('getPendingApproveCount', () => {
  getPendinApprovalNum()
})

// 🔴 [DEBUG] 监听供应商商品红点更新事件
eventBus.on('updateSupplierProductRedPoint', () => {
  getRedPoint()
})
const handleNavigateHelp = () => {
  window.open('https://doc.weixin.qq.com/doc/w3_AecA6gZ8ALICNBx3dr89XRLyBHnin?scode=AOIABQfcAGADSUU6C5AecA6gZ8ALI')
}

const handlePersonalCenter = async () => {
  // await formStore.checkFormChangedAndConfirm()

  window.open('https://westmonth.com/personal/securityCenter')
}
onMounted(() => {
  getNotice()
  checkEdition()
  setInterval(
    () => {
      checkEdition()
    },
    1000 * 60 * 5,
  )
  getUserData()

  // 添加错误处理
  try {
    appStore.getLogOpenVisible()
  } catch (error) {
    console.warn('获取日志设置失败，可能是权限问题:', error)
  }

  getPendinApprovalNum()
  setInterval(
    () => {
      getPendinApprovalNum()
    },
    1000 * 60 * 5,
  )

  // 获取菜单红点数字
  getRedPoint()
  badgeStore.startTimer(1000 * 60 * 5)
})

onUnmounted(() => {
  if (timer) clearTimeout(timer)
  badgeStore.stopTimer()
})

watch(
  () => router.currentRoute.value.path,
  (newValue) => {
    try {
      const userData = localStorage.getItem('userData')
      if (!userData) return

      const arr = JSON.parse(userData).permissions_infos

      // 如果没有权限列表且不是错误页面，跳转到错误页面
      if ((!arr || arr.length === 0) && !newValue.startsWith('/404')) {
        // 确保权限检查完成状态已设置
        permissionCheckCompleted.value = true
        nextTick(() => {
          router.push('/404')
        })
        return
      }

      let item: any
      arr?.forEach((x) => {
        if (x.path === newValue) item = x
        if (x.children?.length != 0) {
          x.children?.forEach((y) => {
            if (y.path === newValue) {
              item = y
            }
            if (y.children && y.children?.length != 0) {
              y.children?.forEach((z) => {
                if (z.path === newValue) {
                  item = z
                }
              })
            }
          })
        }
      })
      if (item) {
        if (navPageArr.value.findIndex((e) => e.path === item.path) === -1) navPageArr.value.push(item)
        localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
        checkRouter(newValue)
      }
    } catch (error) {
      console.log(error)
    }
  },
  { immediate: true },
)

// 处理部门名称：企业子账号显示第一个>后的内容
// const formattedDepartment = computed(() => {
//   // 非企业子账号直接返回原始部门信息
//   if (account.value?.account_type !== '企业子账号') {
//     return userData.value.department?.[0]?.full_name || userData.value.department_name
//   }

//   // 企业子账号：从full_name中提取>后的内容
//   const fullName = userData.value.department?.[0]?.full_name || userData.value.department_name
//   if (fullName.includes('>')) {
//     // 分割后取第一个>后面的部分（trim去除空格）
//     return fullName.split('>')[1]?.trim() || fullName
//   }

//   // 没有>时返回原始值
//   return fullName
// })

// 修改后的计算属性：使用logToStorage记录日志
const formattedDepartment = computed(() => {
  // 非企业子账号逻辑
  if (userData.value?.account_type !== '企业子账号') {
    const result = userData.value.department?.[0]?.full_name || userData.value.department_name

    return result
  }

  // 企业子账号逻辑
  const fullName = userData.value.department?.[0]?.full_name || userData.value.department_name

  if (fullName.includes('>')) {
    const splitResult = fullName.split('>')[1]?.trim() || fullName

    return splitResult
  }

  return fullName
})
</script>
<style lang="scss">
// 全局覆盖ant-badge-count的阴影样式，移除box-shadow
:where(.css-dev-only-do-not-override-14rd88r).ant-badge .ant-badge-count {
  min-width: 30px !important;
  box-shadow: none !important;
}

// 针对组件内的badge单独处理（如果上面的全局样式不生效）
.box {
  :deep(.ant-badge .ant-badge-count) {
    min-width: 30px !important;
    box-shadow: none !important;
  }
}

// 菜单红点样式优化
:deep(.ant-menu) {
  .ant-badge {
    .ant-badge-count {
      min-width: 30px !important;
      box-shadow: none !important;
    }

    .ant-badge-dot {
      box-shadow: none !important;
    }
  }
}

// 强制去掉所有badge的阴影
:deep(.ant-badge-count) {
  min-width: 30px !important;
  box-shadow: none !important;
}

:deep(.ant-badge-dot) {
  box-shadow: none !important;
}

// 商品管理菜单图标红点样式
.menu-icon-wrapper {
  position: relative;
  display: inline-block;

  .menu-red-dot {
    position: absolute;
    top: 1px;
    right: -4px;
    z-index: 10;
    width: 5px;
    height: 5px;
    background: #ff4d4f;
    border-radius: 50%;

    // border: 1px solid #fff;
    box-shadow: none !important;
  }
}
</style>
<style lang="scss" scoped>
.page {
  box-sizing: border-box;
  display: flex;
  min-width: 1200px;
  min-height: 100vh;

  :deep(.ant-menu-dark) {
    background-color: rgb(0 0 0 / 0%);

    .ant-menu-item {
      border-left: 3px solid transparent;
    }
  }

  :deep(.ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline) {
    background-color: rgb(0 0 0 / 10%);
  }

  :deep(.ant-menu-dark .ant-menu-item-selected) {
    color: #7ebcfc;
    background-color: rgb(64 158 255 / 15%);
    border-left: 3px solid #7ebcfc;
  }

  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title),
  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected)) {
    &:hover,
    &:active {
      color: #7ebcfc;
      background-color: rgb(64 158 255 / 15%);
    }
  }

  :deep(.ant-menu-inline .ant-menu-item),
  :deep(.ant-menu-vertical .ant-menu-item),
  :deep(.ant-menu-inline .ant-menu-submenu-title),
  :deep(.ant-menu-vertical .ant-menu-submenu-title) {
    width: 100%;
    margin-inline: 0;
    border-radius: 0;
  }

  :deep(.ant-menu-inline-collapsed) {
    width: 62px;
  }

  :deep(.ant-menu-inline > .ant-menu-item),
  :deep(.ant-menu-vertical > .ant-menu-item),
  :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title),
  :deep(.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title) {
    height: 40px;
    line-height: 40px;
  }

  .memuBox {
    width: 180px;
    background-color: #1f1f3f;

    // flex-shrink: 0;
    // transition: all 1s;
    &.memuActive {
      width: 62px;
    }

    .titleBox {
      position: relative;
      min-width: 100%;
      height: 50px;
      background-color: #1f1f3f;

      .title {
        display: flex;
        justify-content: center;

        // padding: 10px 0 10px 14px;
        padding-block: 10px;
        overflow: hidden;
        font-size: 18px;
        font-weight: bold;
        color: #fff;

        .logo {
          width: 30px;
          height: 30px;
        }

        .text {
          margin-left: 10px;
          line-height: 30px;
          text-wrap: nowrap;
        }
      }

      .toggleBtn {
        position: absolute;
        top: 0;
        left: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 40px;
        padding: 0;
        color: #999;
        cursor: pointer;
        background-color: rgb(235 235 235);
        border-radius: 0;
      }
    }

    .version {
      height: 34px;
      font-size: 11px;
      line-height: 34px;
      color: #999;
      text-align: center;
      user-select: none;
      background-color: #1f1f3f;

      .icon {
        margin-right: 4px;
      }
    }

    .box {
      flex: 1;
      scrollbar-width: none;
      height: calc(100vh - 85px);
      overflow-y: scroll;
      -ms-overflow-style: none;

      .iconfont {
        font-size: 20px;
        color: rgb(255 255 255 / 65%);
      }

      &::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
    }
  }

  .mainBox {
    display: flex;
    flex-direction: column;
    width: calc(100% - 180px);

    // transition: all 1s;
    height: 100vh;

    &.active {
      width: calc(100% - 62px);
    }

    .topNav {
      display: flex;
      align-items: center;
      justify-content: end;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 20px 0 35px;
      background: rgb(235 235 235);

      .navBox {
        position: relative;
        display: flex;
        flex: 1;
        align-items: flex-end;
        height: 100%;
        margin-right: 24px;
        overflow: hidden;

        :deep(.ant-tabs) {
          width: 100%;
          line-height: 14px;
        }

        :deep(.ant-tabs-nav) {
          margin-bottom: 0;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-nav-list) {
          background: linear-gradient(to bottom, rgb(235 235 235) 50%, white 50%);
        }

        :deep(.ant-tabs-tab) {
          position: relative;
          height: 32px;
          padding: 0 10px;
          font-size: 12px;
          color: #999;
          background-color: rgb(235 235 235);
          border: none;
          border-radius: 8px 8px 0 0;

          &::before {
            position: absolute;
            left: 0;
            width: 1px;
            height: 10px;
            content: '';
            background-color: #d9d9d9;
          }

          // top: -1px;
          .ant-tabs-tab-remove {
            padding: 0;
            padding-right: 4px;
            color: #999 !important;
          }

          .ant-tabs-tab-btn {
            transition: none;
          }
        }

        :deep(.ant-tabs-tab-with-remove) {
          .ant-tabs-tab-remove {
            color: white;
          }
        }

        :deep(.ant-tabs-tab-active) {
          position: relative;
          top: 0;
          color: #000;
          background-color: #fff;

          .ant-tabs-tab-remove {
            color: #000;
          }

          .ant-tabs-tab-btn {
            color: #000;
            text-shadow: none;
          }

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab-active) + .ant-tabs-tab {
          border-bottom-left-radius: 8px;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab:has(+ .ant-tabs-tab-active)) {
          border-bottom-right-radius: 8px;
        }

        :deep(.anticon-ellipsis) {
          color: #fff;
        }

        :deep(.ant-tabs-nav-more) {
          position: relative;
          top: -1px;
          padding: 6px 8px;
          background: rgb(235 235 235);

          .anticon-ellipsis {
            color: #999;
          }
        }
      }

      .btnBox {
        display: flex;
        align-items: center;

        .btnBoxMainText {
          padding: 12px;

          // font-size: @font-size-base;
          color: #333;
          text-wrap: nowrap;
          cursor: pointer;
          transition: color 0.3s;

          .btnBoxsubText {
            padding-left: 8px;
            font-size: 12px;
          }
        }

        .btn {
          margin-left: 10px;
        }
      }
    }

    .msgAlert {
      padding: 4px 12px;
      overflow: hidden; /* 隐藏溢出内容 */
      font-size: 14px;
      white-space: nowrap; /* 不换行 */
      border: 1px solid #ffe58f;

      ::v-deep(.ant-alert-content) {
        position: relative;
        height: 20px;
        overflow: hidden;
      }

      ::v-deep(.ant-alert-message) {
        position: absolute;
        display: inline-block;
        animation: scroll var(--duration) linear infinite; /* 动画效果，56秒完成一次循环，速度更慢 */
      }
    }

    @keyframes scroll {
      0% {
        right: 0%; /* 开始位置在右侧 */
        transform: translateX(100%);
      }

      100% {
        right: 100%; /* 结束位置在左侧 */
      }
    }

    .boxStr {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      flex-direction: column;
      width: 100%;
      padding: 12px;
      overflow: hidden scroll;

      &::-webkit-scrollbar {
        display: none;
        width: 0 !important;
        height: 0 !important;
        appearance: none;
        background: transparent;
      }
    }
  }
}

.conternBox {
  box-sizing: border-box;
  width: 330px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;
  box-shadow:
    0 3px 6px -4px #0000001f,
    0 6px 16px #00000014,
    0 9px 28px 8px #0000000d;

  .nameBox {
    box-sizing: border-box;
    display: flex;
    padding: 16px 20px;
    background: #fff;

    .nameImg {
      width: 44px;
      min-width: 44px;
      height: 44px;
      min-height: 44px;
      margin-right: 12px;
      overflow: hidden;

      // background-color: #000;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .nameText {
      .textTop {
        font-size: 14px;
        font-weight: 500;
        line-height: 25px;
        color: #111;
      }

      .textBot {
        display: flex;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        color: #999;

        .label {
          white-space: nowrap;
        }
      }
    }
  }

  .tuiChu {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .tuiChu:hover {
    color: #409eff;
    background-color: #eef2fa;
  }

  .editInfoBtn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .editInfoBtn:hover {
    color: #409eff;
    background: #eef2fa;
  }
}
/* 权限检查加载页面样式 */
.permission-loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: #fff;
}

.loading-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.loading-text {
  font-size: 14px;
  color: #666;
}
</style>

<style>
.ant-menu-dark.ant-menu-submenu > .ant-menu {
  margin-left: -12px;
  background-color: #2a2a48;
}

.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active,
.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
}

.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item,
.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title {
  width: 100%;
  margin-inline: 0;
  border-radius: 0;
}

.ant-menu-dark .ant-menu-item-selected {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
  border-left: 3px solid #7ebcfc;
}

.ant-alert-message {
  font-size: 12px;
}
</style>
