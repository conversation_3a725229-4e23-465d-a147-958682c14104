import { Form, FormItem, Col, Row, Input, InputNumber as AInputNumber, Select, Cascader, type FormInstance, DatePicker, Radio, Checkbox } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { defineComponent, h } from 'vue'
import type { BaseFormItem, BaseFormProps } from './type'
import InputNumber from './components/InputNumber.vue'

// 表单项内容
const FormItemContent = defineComponent({
  name: 'FormItemContent',
  props: {
    item: {
      type: Object as PropType<BaseFormItem>,
      required: true,
    },
    value: {
      type: [String, Number, Boolean, Array, Object] as PropType<any>,
      default: undefined,
    },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    // 处理响应式的 isText
    const renderFormItem = (item: BaseFormItem) => {
      const defaultPlaceholder = `请${item.type === 'select' ? '选择' : '输入'}${typeof item.label === 'string' ? item.label : ''}`
      const commonProps = {
        value: props.value,
        'onUpdate:value': (val: any) => emit('update:value', item.type === 'a-input-number' && val ? Number(val) : val),
      }

      switch (item.type) {
        case 'input':
          return h(Input, { showCount: true, placeholder: item.props?.placeholder || defaultPlaceholder, ...commonProps, ...item.props }, item.slots)
        case 'select':
          return h(Select, { placeholder: item.props?.placeholder || defaultPlaceholder, ...commonProps, ...item.props }, item.slots)
        case 'textarea':
          return h(Input.TextArea, { showCount: true, placeholder: item.props?.placeholder || defaultPlaceholder, ...commonProps, ...item.props }, item.slots)
        case 'input-number':
          return h(InputNumber, { class: 'w-full', placeholder: item.props?.placeholder || defaultPlaceholder, ...commonProps, ...item.props }, item.slots)
        case 'a-input-number':
          return h(AInputNumber, { class: 'w-full', placeholder: item.props?.placeholder || defaultPlaceholder, ...commonProps, ...item.props }, item.slots)
        case 'date-picker':
          return h(DatePicker, { class: 'w-full', ...commonProps, ...item.props }, item.slots)
        case 'cascader':
          return h(Cascader, { ...commonProps, ...item.props }, item.slots)
        case 'text':
          return h('span', { ...item.props, class: 'a-text-wrap' }, { default: () => props.value || item.default })
        case 'radio-group':
          return h(Radio.Group, { ...commonProps, ...item.props }, item.slots)
        case 'checkbox-group':
          return h(Checkbox.Group, { ...commonProps, ...item.props }, item.slots)
        case 'slot':
          return typeof item.slots === 'function' ? item.slots() : item.slots
        default:
          return null
      }
    }

    return () => (
      <div class="flex">
        {renderFormItem(props.item)}
        {props.item.type !== 'title' && props.item.afterSlot?.()}
      </div>
    )
  },
})

// 基础表单
export default defineComponent({
  name: 'BaseForm',
  props: {
    formConfig: {
      type: Object as PropType<Ref<BaseFormItem[]>>,
      default: () => ref([]),
    },
    formProps: {
      type: Object as PropType<BaseFormProps>,
      default: () => ({}),
    },
    modelValue: {
      type: Object as PropType<Ref<any>>,
      required: true,
    },
    isText: {
      type: [Object, Boolean] as PropType<Ref<boolean> | boolean>,
      default: () => false,
    },
    formRef: {
      type: Object as PropType<Ref<FormInstance | undefined>>,
      default: () => {},
    },
    rules: {
      type: Object as PropType<Ref<Record<string, Rule[]> | Record<string, Rule> | undefined>>,
      default: () => ({}),
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const form = computed({
      get() {
        return props.modelValue.value
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    return () => (
      <div class="base-form">
        <Form layout="vertical" model={form.value} {...props.formProps} rules={unref(props.rules)} ref={props.formRef}>
          <Row gutter={16} class="px-20">
            {props.formConfig.value
              .filter((item) => !item.hidden)
              .map((item) => (
                <Col span={item.span || 24} key={item.type === 'title' ? item.label : item.key}>
                  {item.type === 'title' ? (
                    <div class="drawer-title w-[calc(100%+40px)] ml-[-20px]">{typeof item.label === 'string' ? item.label : item.label()}</div>
                  ) : (
                    <FormItem name={item.key}>
                      {{
                        label: () => (typeof item.label === 'string' ? item.label : item.label?.()),
                        default: () =>
                          unref(props.isText && item.type !== 'slot') ? (
                            <span class="a-text-wrap">
                              {form.value[item.key] || '--'}
                              {item.afterSlot?.()}
                            </span>
                          ) : (
                            <FormItemContent item={item} v-model={[form.value[item.key], 'value']} />
                          ),
                      }}
                    </FormItem>
                  )}
                </Col>
              ))}
          </Row>
        </Form>
      </div>
    )
  },
})

export type { BaseFormItem, BaseFormProps }
