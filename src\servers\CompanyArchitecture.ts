// 组织架构管理接口 - 新的 /XY/Company/ 路径
import { requestXY } from './request'

// ==================== 企业管理接口 ====================

// 新增企业
export const AddCompany = (data: { id: string; name: string; status: number }) => {
  return requestXY({ url: '/Company/AddCompany', data }, 'POST')
}

// 修改企业信息
export const EditCompany = (data: { id: string; name: string; status: number }) => {
  return requestXY({ url: '/Company/EditCompany', data }, 'POST')
}

// 更新企业状态
export const UpdateStatusCompany = (data: { id: string; name: string; status: number }) => {
  return requestXY({ url: '/Company/UpdateStatusCompany', data }, 'POST')
}

// ==================== 部门管理接口 ====================

// 新增部门
export const AddDepartment = (data: { name: string; p_id?: string; header_ids?: number }) => {
  return requestXY({ url: '/Company/AddDepartment', data }, 'POST')
}

// 编辑部门
export const EditDepartment = (data: { id: string; name: string; p_id?: string; header_ids?: number }) => {
  return requestXY({ url: '/Company/EditDepartment', data }, 'POST')
}

// 删除部门
export const DeleteDepartment = (data: { id: string; p_id: string; company_id: string; name: string; header_ids: number; oa_id: string }) => {
  return requestXY({ url: '/Company/DeleteDepartment', data }, 'POST')
}

// 部门上移/下移
export const DepartmentMove = (data: {
  company_id: string
  department_id: string
  direction: boolean // true=上移, false=下移
}) => {
  return requestXY({ url: '/Company/DepartmentMove', data }, 'POST')
}

// ==================== 单位管理接口 ====================

// 新增单位
export const AddUnit = (data: {
  name: string
  p_id?: string // 上级id - 创建子单位时传入父单位id，创建部门时为必填项，传入单位id/父部门id
  company_id?: string
  header_ids?: number
}) => {
  return requestXY({ url: '/Company/AddUnit', data }, 'POST')
}

// 修改单位
export const EditUnit = (data: { id: string; p_id: string; company_id: string; name: string; header_ids: string[]; oa_id: string; status: number }) => {
  return requestXY({ url: '/Company/EditUnit', data }, 'POST')
}

// 删除单位
export const DeleteUnit = (data: { id: string; p_id: string; company_id: string; name: string; header_ids: string[]; oa_id: string; status: number }) => {
  return requestXY({ url: '/Company/DeleteUnit', data }, 'POST')
}

// ==================== 数据获取接口 ====================

// 获取组织架构树
export const GetCompanyTree = (data: {
  company_id?: number // 企业ID，默认为当前用户的企业ID
}) => {
  return requestXY({ url: '/Company/GetCompanyTree', data }, 'POST')
}

// 获取单位列表
export const UnitGetList = (data: { company_id: string }) => {
  return requestXY({ url: '/Company/UnitGetList', data }, 'POST')
}

// 获取部门详情 - 旧接口（已废弃）
export const GetDepartmentDetail = (data: { id: string }) => {
  return requestXY({ url: '/Company/GetDepartmentDetail', data }, 'POST')
}

// 根据ID获取企业/单位/部门详细信息 - 新接口
export const GetCompanyById = (data: { id: string; type: number }) => {
  return requestXY({ url: '/Company/GetCompanyById', data }, 'POST')
}

// 获取操作日志（通用）
export const GetOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: string; account_id?: string }) => {
  return requestXY({ url: '/Company/GetOpLogInfos', data }, 'GET')
}

// 获取部门操作日志
export const GetDepartmentOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: string; account_id?: string }) => {
  return requestXY({ url: '/Company/GetDepartmentOpLogInfos', data }, 'POST')
}

// 获取单位操作日志
export const GetUnitOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: string; account_id?: string }) => {
  return requestXY({ url: '/Company/GetUnitOpLogInfos', data }, 'POST')
}

// 获取企业操作日志
export const GetCompanyOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: string; account_id?: string }) => {
  return requestXY({ url: '/Company/GetCompanyOpLogInfos', data }, 'POST')
}

// ==================== 类型定义 ====================

// 组织架构树节点数据结构
export interface TreeNode {
  id_with_type: string // ID拼接Type的一个字段, 组织架构树ID使用该字段, 仅作为ID不重复的一个标识, 请勿将其用于业务逻辑
  p_id_with_type: string // 父级ID拼接父级type，用于构建架构树子节点的唯一标识, 请勿将其用于业务逻辑
  id: number // 节点ID
  p_id: number // 父节点ID
  parent_type: '企业=1' | '单位=2' | '部门=3' // 父级类型
  name: string // 名称
  full_name: string // 全称
  type: '企业=1' | '单位=2' | '部门=3' // 节点类型
  order: number // 排序
  oa_id: number // OA系统ID
  level?: number // 层级（前端计算）
  class?: string // CSS类名（前端计算）
  childs: TreeNode[] // 子节点

  // 兼容性字段（前端使用）
  department_name?: string // 兼容旧的部门名称字段
  company_name?: string // 兼容旧的企业名称字段
}

// 企业/单位/部门详情数据结构（GetCompanyById接口返回）
export interface CompanyDetailInfo {
  type: number // 类型：1=企业, 2=单位, 3=部门
  type_name: string // 类型名称
  number: string // 编号
  source: 'UMC' | string // 来源
  source_name: string // 来源名称
  name: string // 名称
  company_id: string // 企业ID
  company_name: string // 企业名称
  parent_company_name: string // 上级企业名称
  header_info: Array<{
    id: string
    name: string
    full_name: string
  }> // 负责人信息
  create_at: string // 创建时间
  create_name: string // 创建人
  update_at: string // 更新时间
  update_name: string // 更新人
}

// 搜索选项数据结构
export interface SearchOption {
  id: string // 部门ID
  // department_name: string // 部门名称
  name: string // 部门名称
  // company_name: string // 企业路径
  full_name: string // 企业路径
  type: number // 节点类型
}

// API响应数据结构
export interface ApiResponse<T = any> {
  code: number
  success: boolean
  message: string
  data?: T
}

// 分页响应数据结构
export interface PageResponse<T = any> {
  code: number
  success: boolean
  message: string
  data: {
    list: T[]
    total: number
  }
}

// 操作日志数据结构
export interface OpLogInfo {
  user_name: string
  user_department: string
  edits: {
    fie_id: string
    name: string
    old_val: string
    new_val: string
  }[]
  op_type: string
  op_at: string
}

// 单位信息数据结构
export interface UnitInfo {
  id: string
  name: string
  full_name: string
}

// ==================== 工具函数 ====================

// 从登录回调数据中获取 company_id
export const getCompanyIdFromUserData = (): string | null => {
  try {
    const userData = localStorage.getItem('userData')
    if (userData) {
      const userDataObj = JSON.parse(userData)
      return userDataObj.company_id || userDataObj.company?.id || null
    }
    return null
  } catch (error) {
    console.error('获取 company_id 失败:', error)
    return null
  }
}

// 获取组织架构树（自动使用当前用户的 company_id）
export const GetCompanyTreeAuto = (data?: {
  company_id?: number // 企业ID，可选，如果不传则使用当前用户的企业ID
}) => {
  const userCompanyId = getCompanyIdFromUserData()
  const companyId = data?.company_id || (userCompanyId ? parseInt(userCompanyId) : undefined)
  return GetCompanyTree({
    company_id: companyId,
  })
}

// ==================== 兼容性接口 ====================

// 兼容旧的接口名称，保持向后兼容
export const GetCompanyList = GetCompanyTreeAuto
export const GetDepartmentList = GetCompanyTreeAuto

// 导出所有接口
export default {
  // 企业管理
  AddCompany,
  EditCompany,
  UpdateStatusCompany,

  // 部门管理
  AddDepartment,
  EditDepartment,
  DeleteDepartment,
  DepartmentMove,

  // 单位管理
  AddUnit,
  EditUnit,
  DeleteUnit,

  // 数据获取
  GetCompanyTree,
  GetCompanyTreeAuto,
  GetDepartmentDetail,
  GetCompanyById,
  UnitGetList,
  GetOpLogInfos,

  // 工具函数
  getCompanyIdFromUserData,

  // 兼容性接口
  GetCompanyList,
  GetDepartmentList,
}
