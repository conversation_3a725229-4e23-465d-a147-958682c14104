<template>
  <a-drawer :width="850" v-model:open="openDrawer" @after-open-change="afterOpenChange">
    <template #title>
      <div class="flex w-full items-center justify-between">
        <span>查看用户</span>
        <div class="flex gap-2">
          <a-button @click="handleOperationLog">操作日志</a-button>
        </div>
      </div>
    </template>

    <a-spin v-show="loading" />
    <div v-if="!loading && memberDetails">
      <div class="drawer-title">基本信息</div>
      <a-form :model="memberDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
        <!-- <a-form-item label="用户编号">
          <span>{{ memberDetails.user_id || '/' }}</span>
        </a-form-item> -->
        <a-form-item label="账号">
          <span>{{ memberDetails.uid || '/' }}</span>
        </a-form-item>
        <!-- <a-form-item label="来源">
          <span>{{ memberDetails.account_source_format || '/' }}</span>
        </a-form-item> -->
        <!-- <a-form-item label="是否同步AD域">
          <span>{{ memberDetails.is_sync_ad_format || '/' }}</span>
        </a-form-item> -->
        <!-- <a-form-item label="别名">
          <span>{{ memberDetails.account_name || '/' }}</span>
        </a-form-item> -->
        <a-form-item label="账号类型">
          <span>{{ memberDetails.account_type || '/' }}</span>
        </a-form-item>
        <a-form-item label="姓名">
          <span>{{ memberDetails.real_name || '/' }}</span>
        </a-form-item>

        <a-form-item label="工号" v-if="isInternalUser">
          <span>{{ memberDetails.job_id || '/' }}</span>
        </a-form-item>
        <!-- <a-form-item :label="getCompanyLabelForMember()">
          <span>{{ getCompanyValueForMember() }}</span>
        </a-form-item> -->
        <a-form-item label="所属企业" v-if="true || shouldShowBothForMember()">
          <span>{{ memberDetails.company_name || '/' }}</span>
        </a-form-item>
        <a-form-item label="所属部门">
          <div v-if="memberDetails.department && memberDetails.department.length > 0" class="pt-3px">
            <div v-for="(dept, index) in memberDetails.department" :key="dept.id || index">
              <a-tooltip :title="dept.full_name" placement="topLeft">
                <span>{{ dept.name || '未知部门' }}</span>
                <span v-if="dept.is_main === 1" class="ml-10px">主部门</span>
                <!-- <span class="ml-10px">{{ dept.is_main_format }}</span> -->
                <div class="text-11px text-gray">
                  {{ dept.full_name || '/' }}
                </div>
              </a-tooltip>
            </div>
          </div>
          <span v-else>/</span>
        </a-form-item>
        <a-form-item label="所属岗位" v-if="isInternalUser">
          <span>{{ memberDetails.position_name || '/' }}</span>
        </a-form-item>
        <a-form-item label="直接上级">
          <span>{{ formatLeaderInfo(memberDetails) }}</span>
        </a-form-item>
        <a-form-item label="账号状态">
          <span>{{ memberDetails.status == 0 ? '停用' : '启用' }}</span>
        </a-form-item>
        <!-- <a-form-item label="所属用户">
          <span>{{ memberDetails.user_id || '/' }}</span>
        </a-form-item>
        <a-form-item label="用户名">
          <span>{{ memberDetails.user_name || '/' }}</span>
        </a-form-item> -->
        <a-form-item label="绑定手机">
          <div v-if="memberDetails.phones && memberDetails.phones.length > 0">
            <span v-for="(phone, index) in memberDetails.phones" :key="index">
              {{ phone }}
              <span v-if="index < memberDetails.phones.length - 1">:</span>
            </span>
          </div>
          <span v-else>/</span>
        </a-form-item>
        <a-form-item label="绑定邮箱">
          <div v-if="memberDetails.emails && memberDetails.emails.length > 0">
            <span v-for="(email, index) in memberDetails.emails" :key="index">
              {{ email }}
              <span v-if="index < memberDetails.emails.length - 1">:</span>
            </span>
          </div>
          <span v-else>/</span>
        </a-form-item>
      </a-form>

      <div class="drawer-title">系统信息</div>
      <a-form :model="memberDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
        <a-form-item label="系统角色">
          <div v-if="memberDetails.role_names && memberDetails.role_names.length > 0">
            <div v-for="(role, index) in memberDetails.role_names" :key="index">
              {{ role }}
            </div>
          </div>
          <span v-else>/</span>
        </a-form-item>
      </a-form>

      <div class="drawer-title">其他信息</div>
      <a-form :model="memberDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
        <a-row :gutter="16">
          <!-- 仅保留最后修改时间和最后修改人，显示在同一行 -->
          <a-col :span="12">
            <a-form-item label="最后修改时间">
              <span>{{ memberDetails.update_at || '/' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最后修改人">
              <a-tooltip :title="memberDetails.update_user_department || '暂无部门信息'">
                <span>
                  {{ memberDetails.update_user_name || '/' }}
                </span>
                <span class="ml-10px text-11px text-gray ellipsis">
                  {{
                    memberDetails?.update_user_job_name || memberDetails?.update_user_department
                      ? memberDetails?.update_user_job_name && memberDetails?.update_user_department
                        ? `( ${memberDetails?.update_user_job_name} | ${memberDetails?.update_user_department} )`
                        : memberDetails?.update_user_job_name
                          ? `( ${memberDetails?.update_user_job_name} )`
                          : `( ${memberDetails?.update_user_department} )`
                      : ''
                  }}
                </span>
              </a-tooltip>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-drawer>
  <operate-log ref="operateLogRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GetUserInfo } from '@/servers/UserManager'

import OperateLog from './OperateLog.vue'

// 获取当前用户信息，判断是否为内部用户
const getCurrentUserScope = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      return userData.scope || 2 // 默认为外部用户
    }
    return 2 // 默认为外部用户
  } catch (error) {
    console.error('获取用户scope失败:', error)
    return 2 // 默认为外部用户
  }
}

// 判断是否为内部用户（scope=1为内部用户，scope=2为外部用户）
const isInternalUser = computed(() => getCurrentUserScope() === 1)

const operateLogRef = ref()

interface DepartmentInfo {
  id?: string
  name?: string
  fullPath?: string
  is_main?: number
  full_name?: string
}

interface MemberDetails {
  id?: string | null
  uid?: string | null // 账号
  status_format?: string | null // 账号状态
  job_id?: string | null // 工号
  is_sync_ad_format?: string | null // 是否同步AD域
  account_name?: string | null // 账号用户名称
  account_source_format?: string | null // 账号来源
  account_type_format?: string | null // 账号类型
  real_name?: string | null // 真实姓名
  company_name?: string | null // 所属企业
  department?: DepartmentInfo[] | null // 所属部门
  user_id?: string | null // 用户编号
  user_name?: string | null // 用户名
  position_name?: string | null // 所属岗位
  leader_name?: string | null // 直接上级姓名
  leader_job_id?: string | null // 直接上级ID
  role_names?: string[] | null // 角色
  phones?: string[] | null // 绑定手机
  emails?: string[] | null // 绑定邮箱
  create_at?: string | null // 创建时间
  update_at?: string | null // 最后修改时间
  update_user_department?: string | null // 最后修改人部门
  update_user_name?: string | null // 最后修改人
  update_user_job_name?: string | null // 最后修改人岗位
  create_user_name?: string | null // 创建人
  create_user_department?: string | null // 创建人部门
  create_user_job_name?: string | null // 创建人岗位
  account_type?: number | null // 账号类型状态
  oa_name?: string | null // 关联OA成员
  // 添加 unit_name 属性（根据实际数据类型定义，这里假设为字符串或 null）
  unit_name?: string | null // 新增：单位名称字段
  status: number
}

const selectId = ref<string | null>(null)
const openDrawer = ref(false)
const loading = ref(false)

// 初始化空的成员详情
const initialMemberDetails: MemberDetails = {
  id: null,
  uid: null,
  status_format: null,
  job_id: null,
  is_sync_ad_format: null,
  account_name: null,
  account_source_format: null,
  account_type_format: null,
  real_name: null,
  company_name: null,
  department: [],
  user_id: null,
  user_name: null,
  position_name: null,
  leader_name: null,
  leader_job_id: null,
  role_names: [],
  phones: [],
  emails: [],
  create_user_job_name: null,
  create_user_department: null,
  update_user_job_name: null,
  update_user_department: null,
  account_type: null,
  oa_name: null,
  status: 0,
}

const memberDetails = ref<MemberDetails>({ ...initialMemberDetails })

// 格式化直接上级信息
const formatLeaderInfo = (details: MemberDetails) => {
  if (!details.leader_name) {
    return '/'
  }

  // 如果有leader_job_id，格式化为 "姓名(ID)"
  if (details.leader_job_id) {
    return `${details.leader_name}(${details.leader_job_id})`
  }

  return details.leader_name
}

// 获取成员详情
const getMemberInfo = async (userId: string) => {
  try {
    console.log('获取用户详情，使用ID:', userId)

    const res = await GetUserInfo({
      umcAccountId: userId,
    })

    if (res.data) {
      memberDetails.value = res.data
      selectId.value = res.data.id
      console.log('获取到的用户信息:', res.data)
    } else {
      console.warn('接口返回空数据')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
  }
}

// 打开抽屉
const showDrawer = async (row: any) => {
  console.log('SimpleUserInfoDrawer 打开，row数据:', row)
  openDrawer.value = true
  loading.value = true
  memberDetails.value = { ...initialMemberDetails }

  // 尝试使用不同的ID字段，根据接口文档，umcAccountId应该是账号ID
  // 优先尝试 account_id，然后是 id，最后是 user_id
  const userId = row.account_id || row.id || row.user_id
  console.log('使用用户ID:', userId, '可用字段:', {
    account_id: row.account_id,
    id: row.id,
    user_id: row.user_id,
  })

  if (!userId) {
    console.error('无法找到有效的用户ID')
    loading.value = false
    return
  }

  await getMemberInfo(row.id)
  loading.value = false
}

// 抽屉状态改变后的回调
const afterOpenChange = (status: boolean) => {
  if (!status) {
    // 清空数据
    memberDetails.value = { ...initialMemberDetails }
  }
}

// 查看操作日志
const handleOperationLog = () => {
  operateLogRef.value?.open(selectId.value || '')
}

// 判断是否需要同时显示企业和单位信息
const shouldShowBothForMember = () => {
  if (!memberDetails.value) return false
  // 只有当有单位信息且企业信息与单位信息不同时，才显示企业信息
  return memberDetails.value.unit_name && memberDetails.value.company_name && memberDetails.value.unit_name !== memberDetails.value.company_name
}

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
.drawer-title {
  padding-bottom: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

.ellipsis {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}

.pt-3px {
  padding-top: 3px;
}

.ml-10px {
  margin-left: 10px;
}

.text-11px {
  font-size: 11px;
}

.text-gray {
  color: #999;
}

:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    text-align: left;
  }
}

.flex {
  display: flex;
}

.w-full {
  width: 100%;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}
</style>
